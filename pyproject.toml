[tool.mypy]
python_version = "3.11"
ignore_missing_imports = true
files = "dinov3"
exclude = '''(?x)(
  ^dinov3/tests/([^/]+/)*test_.*\.py$  # Unit tests
)'''

[tool.pylint.master]
persistent = false
score = false

[tool.pylint.messages_control]
disable = "all"
enable = [
  "miscellaneous",
  "similarities",
]

[tool.pylint.similarities]
ignore-comments = true
ignore-docstrings = true
ignore-imports = true
min-similarity-lines = 8

[tool.pylint.reports]
reports = false

[tool.pylint.miscellaneous]
notes = [
  "FIXME",
  "XXX",
  "TODO",
]

[tool.ruff]
line-length = 120
target-version = "py311"

[tool.ruff.lint]
ignore = ["E203", "E501"]

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401"]
"hubconf.py" = ["F401"]
