import sys
import os
import torch
import torch.nn as nn

# Add the DINOv3 repo path
REPO_DIR = "/storage/pmj/zhi/project/dinov3-main"
sys.path.append(REPO_DIR)

from dinov3.models.vision_transformer import DinoVisionTransformer
from dinov3.eval.dense.depth.models.dpt_head import D<PERSON><PERSON><PERSON>_decoder


def create_multiscale_decoder(embed_dim=1024, features=256):
    """Create DPTHead_decoder for multi-scale feature fusion."""
    decoder = DPTHead_decoder(
        in_channels=embed_dim,
        features=features,
        use_bn=False,
        out_channels=[256, 512, 1024, 1024],
        use_clstoken=True
    )
    return decoder


def extract_multiscale_features(backbone, decoder, x, layer_indices=[4, 11, 17, 23]):
    """Extract and fuse multi-scale features."""
    # Get intermediate layer features
    intermediate_features = backbone.get_intermediate_layers(
        x,
        n=layer_indices,
        reshape=False,
        return_class_token=True,
        return_extra_tokens=False,
        norm=True
    )
    
    # Calculate patch dimensions
    B, C, H, W = x.shape
    patch_h = H // backbone.patch_size
    patch_w = W // backbone.patch_size
    
    # Fuse features through decoder
    fused_features = decoder(intermediate_features, patch_h, patch_w)
    
    return fused_features


class Feature_extractor(nn.Module):
    """Encapsulates DINOv3 backbone + DPT multi-scale decoder into a single module.
    Forward returns a 4-level pyramid (1/4,1/8,1/16,1/32) given an input image tensor.
    """
    def __init__(self, checkpoint_path: str, layer_indices=None, features: int = 256, device: str | None = None):
        super().__init__()
        self.checkpoint_path = checkpoint_path
        self.layer_indices = layer_indices or [4, 11, 17, 23]
        self.device = device or ("cuda" if torch.cuda.is_available() else "cpu")
        if not os.path.exists(checkpoint_path):
            raise FileNotFoundError(f"Checkpoint not found: {checkpoint_path}")
        # Build backbone (inline) then load weights
        print(f"Building DINOv3 ViT-L/16 backbone and loading weights from: {checkpoint_path}")
        self.backbone = DinoVisionTransformer(
            img_size=224,
            patch_size=16,
            in_chans=3,
            pos_embed_rope_base=100,
            pos_embed_rope_normalize_coords="separate",
            pos_embed_rope_rescale_coords=2,
            pos_embed_rope_dtype="fp32",
            embed_dim=1024,
            depth=24,
            num_heads=16,
            ffn_ratio=4,
            qkv_bias=True,
            drop_path_rate=0.0,
            layerscale_init=1.0e-05,
            norm_layer="layernormbf16",
            ffn_layer="mlp",
            ffn_bias=True,
            proj_bias=True,
            n_storage_tokens=4,
            mask_k_bias=True,
            untie_global_and_local_cls_norm=False,
        )
        state_dict = torch.load(checkpoint_path, map_location="cpu")
        self.backbone.load_state_dict(state_dict, strict=True)
        print("✅ Backbone weights loaded.")
        # Decoder
        self.decoder = create_multiscale_decoder(embed_dim=self.backbone.embed_dim, features=features)
        self.to(self.device)
        self.backbone.eval()
        self.decoder.eval()
    
    @torch.no_grad()
    def forward(self, x: torch.Tensor):
        x = x.to(self.device)
        intermediate = self.backbone.get_intermediate_layers(
            x,
            n=self.layer_indices,
            reshape=False,
            return_class_token=True,
            return_extra_tokens=False,
            norm=True,
        )
        B, C, H, W = x.shape
        patch_h = H // self.backbone.patch_size
        patch_w = W // self.backbone.patch_size
        pyramid = self.decoder(intermediate, patch_h, patch_w)
        return pyramid  # (lvl1,lvl2,lvl3,lvl4)
    
    @torch.no_grad()
    def extract(self, x: torch.Tensor):
        return self.forward(x)


def main():
    """Main function demonstrating multi-scale feature extraction."""
    # Configuration
    checkpoint_path = "checkpoint/dinov3_vitl16_pretrain_lvd1689m-8aa4cbdd.pth"
    layer_indices = [4, 11, 17, 23]  # ViT-L layers for multi-scale extraction
    device = "cuda" if torch.cuda.is_available() else "cpu"
    
    print(f"Using device: {device}")
    print("=" * 60)
    print("DINOv3 Multi-Scale Feature Extraction")
    print("=" * 60)
    
    # Build feature extractor module
    print("\n1. Building Feature_extractor module (loads backbone + decoder)...")
    extractor = Feature_extractor(checkpoint_path, layer_indices=layer_indices, features=256, device=device)
    
    # 2. Test with sample input
    print("\n2. Testing multi-scale feature extraction...")
    test_input = torch.randn(1, 3, 1024, 2048).to(device)
    
    with torch.no_grad():
        pyramid = extractor(test_input)
        print("   Fused multi-scale features (1/4,1/8,1/16,1/32):")
        for i, feat in enumerate(pyramid):
            scale_name = ["1/4","1/8","1/16","1/32"][i]
            print(f"   Level {i+1} ({scale_name}): {feat.shape}")
    
    print("\n✅ Multi-scale feature extraction completed successfully!")


if __name__ == "__main__":
    main()
