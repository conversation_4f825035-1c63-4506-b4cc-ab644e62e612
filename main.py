import sys
import os

# Add the DINOv3 repo path - current workspace
REPO_DIR = "/storage/pmj/zhi/project/dinov3-main"
sys.path.append(REPO_DIR)

import torch
from torchvision import transforms
import numpy as np
from PIL import Image

from dinov3.models.vision_transformer import DinoVisionTransformer
from dinov3.hub.backbones import dinov3_vitl16
from dinov3.eval.dense.depth.models import build_depther, BackboneLayersSet
from dinov3.eval.dense.depth.models.dpt_head import DPTHead_decoder
import torch.nn.functional as F

intermediate_layer_idx = {
            'vits': [2, 5, 8, 11],
            'vitb': [2, 5, 8, 11], 
            'vitl': [4, 11, 17, 23], 
            'vitg': [9, 19, 29, 39]
        }

def print_model_info(model, model_name="Model"):
    """打印模型的参数信息"""
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"\n{model_name} Info:")
    print(f"  Total parameters: {total_params:,}")
    print(f"  Trainable parameters: {trainable_params:,}")
    print(f"  Model size: {total_params * 4 / 1024**2:.2f} MB (fp32)")


def create_multiscale_feature_extractor(backbone, layer_indices=[4, 11, 17, 23], features=256):
    """
    Create a multi-scale feature extractor using DPTHead_decoder.

    Args:
        backbone: DINOv3 backbone model
        layer_indices: List of layer indices to extract features from
        features: Number of output features for the decoder

    Returns:
        DPTHead_decoder instance configured for the backbone
    """
    print(f"🔧 Creating multi-scale feature extractor for layers {layer_indices}")

    # Get the embedding dimension from the backbone
    embed_dim = backbone.embed_dim
    print(f"   Backbone embedding dimension: {embed_dim}")

    # Create DPTHead_decoder with appropriate configuration
    decoder = DPTHead_decoder(
        in_channels=embed_dim,
        features=features,
        use_bn=False,
        out_channels=[256, 512, 1024, 1024],  # Output channels for each scale
        use_clstoken=True  # Use class token for readout
    )

    print(f"   ✅ Multi-scale decoder created with {features} output features")
    return decoder


def extract_multiscale_features(backbone, decoder, x, layer_indices=[4, 11, 17, 23]):
    """
    Extract multi-scale features from DINOv3 backbone and fuse them using DPTHead_decoder.

    Args:
        backbone: DINOv3 backbone model
        decoder: DPTHead_decoder instance
        x: Input tensor [B, C, H, W]
        layer_indices: List of layer indices to extract features from

    Returns:
        Tuple of fused multi-scale feature maps (path_1, path_2, path_3, path_4)
    """
    # Get intermediate layer features
    intermediate_features = backbone.get_intermediate_layers(
        x,
        n=layer_indices,
        reshape=False,  # Keep as sequence format
        return_class_token=True,
        return_extra_tokens=False,
        norm=True
    )

    # Calculate patch dimensions
    B, C, H, W = x.shape
    patch_h = H // backbone.patch_size
    patch_w = W // backbone.patch_size

    # Process features through decoder
    fused_features = decoder(intermediate_features, patch_h, patch_w)

    return fused_features


def load_dinov3_vitl16_local(checkpoint_path):
    """
    Load DINOv3 ViT-L/16 model with weights from local checkpoint file.
    This bypasses the hub loading mechanism to avoid internet dependencies.
    """
    print(f"🔄 Loading DINOv3 ViT-L/16 model from local checkpoint: {checkpoint_path}")

    # Validate checkpoint file exists and is readable
    if not os.path.exists(checkpoint_path):
        raise FileNotFoundError(f"Checkpoint file not found: {checkpoint_path}")

    if not os.path.isfile(checkpoint_path):
        raise ValueError(f"Path is not a file: {checkpoint_path}")

    # Check file size (should be substantial for a pretrained model)
    file_size = os.path.getsize(checkpoint_path)
    print(f"📊 Checkpoint file size: {file_size / (1024**3):.2f} GB")

    if file_size < 100 * 1024 * 1024:  # Less than 100MB seems too small
        print("⚠️  Warning: Checkpoint file seems unusually small")

    # Create model with the exact same configuration as dinov3_vitl16
    print("🏗️  Creating DINOv3 ViT-L/16 model architecture...")
    model = DinoVisionTransformer(
        img_size=224,
        patch_size=16,
        in_chans=3,
        pos_embed_rope_base=100,
        pos_embed_rope_normalize_coords="separate",
        pos_embed_rope_rescale_coords=2,
        pos_embed_rope_dtype="fp32",
        embed_dim=1024,
        depth=24,
        num_heads=16,
        ffn_ratio=4,
        qkv_bias=True,
        drop_path_rate=0.0,
        layerscale_init=1.0e-05,
        norm_layer="layernormbf16",
        ffn_layer="mlp",
        ffn_bias=True,
        proj_bias=True,
        n_storage_tokens=4,
        mask_k_bias=True,
        untie_global_and_local_cls_norm=False,  # For LVD1689M weights
    )

    # Load state dict directly from local file
    print(f"📂 Loading state dict from: {checkpoint_path}")
    try:
        state_dict = torch.load(checkpoint_path, map_location="cpu")
    except Exception as e:
        raise RuntimeError(f"Failed to load checkpoint file: {str(e)}")

    # Validate state dict structure
    if not isinstance(state_dict, dict):
        raise ValueError("Checkpoint does not contain a valid state dictionary")

    print(f"🔍 State dict contains {len(state_dict)} keys")

    # Check for expected keys
    expected_keys = ['patch_embed.proj.weight', 'blocks.0.attn.qkv.weight']
    missing_keys = [key for key in expected_keys if key not in state_dict]
    if missing_keys:
        print(f"⚠️  Warning: Some expected keys are missing: {missing_keys}")

    # Load the state dict into the model
    print("🔗 Loading weights into model...")
    try:
        missing_keys, unexpected_keys = model.load_state_dict(state_dict, strict=False)
        if missing_keys:
            print(f"⚠️  Missing keys: {len(missing_keys)} keys")
            if len(missing_keys) <= 5:
                print(f"    {missing_keys}")
        if unexpected_keys:
            print(f"⚠️  Unexpected keys: {len(unexpected_keys)} keys")
            if len(unexpected_keys) <= 5:
                print(f"    {unexpected_keys}")

        if len(missing_keys) == 0 and len(unexpected_keys) == 0:
            print("✅ Perfect match: All keys loaded successfully!")
        else:
            print("✅ Weights loaded with some mismatches (this may be normal)")

    except Exception as e:
        raise RuntimeError(f"Failed to load state dict into model: {str(e)}")

    print("✅ Successfully loaded pretrained weights!")
    return model


def load_partial_weights(model: torch.nn.Module, ckpt_path: str, model_name: str = "Model"):
    """Load partial weights for a model, matching only keys with the same shape."""
    if not os.path.exists(ckpt_path):
        print(f"❌ {model_name} checkpoint not found: {ckpt_path}")
        return None
    print(f"🔄 Loading {model_name} checkpoint (partial): {ckpt_path}")
    raw = torch.load(ckpt_path, map_location="cpu")
    if isinstance(raw, dict):
        # common nesting
        for key in ['state_dict', 'model']:
            if key in raw and isinstance(raw[key], dict):
                raw = raw[key]
                break
    if not isinstance(raw, dict):
        print("⚠️  Unexpected checkpoint structure, abort partial load.")
        return None
    cleaned = {}
    for k, v in raw.items():
        nk = k
        for prefix in ["module.", "model."]:
            if nk.startswith(prefix):
                nk = nk[len(prefix):]
        cleaned[nk] = v
    model_state = model.state_dict()
    load_dict = {}
    matched = []
    shape_mismatch = []
    for k, v in cleaned.items():
        if k in model_state:
            if model_state[k].shape == v.shape:
                load_dict[k] = v
                matched.append(k)
            else:
                shape_mismatch.append(k)
    unexpected = [k for k in cleaned.keys() if k not in model_state]
    missing = [k for k in model_state.keys() if k not in cleaned]
    model_state.update(load_dict)
    model.load_state_dict(model_state, strict=False)
    print(f"✅ Matched: {len(matched)}  ⚠️ Shape mismatch: {len(shape_mismatch)}  ⚠️ Unexpected: {len(unexpected)}  ℹ️ Missing: {len(missing)}")
    if matched:
        print(f"  Sample loaded keys: {matched[: min(5, len(matched))]}")
    return {
        'matched': len(matched),
        'shape_mismatch': len(shape_mismatch),
        'unexpected': len(unexpected),
        'missing': len(missing),
    }


def verify_weights_loaded(model, model_name):
    """验证权重是否正确加载"""
    print(f"\n🔍 Verifying weights for {model_name}:")

    # 检查一些关键参数是否不为零（表明权重已加载）
    if hasattr(model, 'patch_embed'):
        patch_embed_weight = model.patch_embed.proj.weight
        print(f"  Patch embedding weight mean: {patch_embed_weight.mean().item():.6f}")
        print(f"  Patch embedding weight std: {patch_embed_weight.std().item():.6f}")

    if hasattr(model, 'blocks') and len(model.blocks) > 0:
        first_block_weight = model.blocks[0].attn.qkv.weight
        print(f"  First attention block weight mean: {first_block_weight.mean().item():.6f}")
        print(f"  First attention block weight std: {first_block_weight.std().item():.6f}")

    # 检查权重是否为随机初始化的值（通常接近0）
    total_abs_sum = sum(p.abs().sum().item() for p in model.parameters())
    print(f"  Total absolute weight sum: {total_abs_sum:.2f}")

    if total_abs_sum > 1000:  # 预训练权重通常有较大的绝对值和
        print("  ✅ Weights appear to be loaded (non-zero values detected)")
    else:
        print("  ⚠️  Weights might be randomly initialized (values close to zero)")


def test_inference_quality(model, model_name):
    """测试推理质量以验证预训练权重的有效性"""
    print(f"\n🧪 Testing inference quality for {model_name}:")

    model.eval()
    with torch.no_grad():
        # 创建两个不同的测试输入
        input1 = torch.randn(1, 3, 224, 224)
        input2 = torch.randn(1, 3, 224, 224)

        # 获取特征
        features1 = model.forward_features(input1)
        features2 = model.forward_features(input2)

        # 提取class token特征
        if isinstance(features1, dict):
            feat1 = features1['x_norm_clstoken']
            feat2 = features2['x_norm_clstoken']
        else:
            feat1 = features1[:, 0]  # class token
            feat2 = features2[:, 0]

        # 计算特征统计
        feat1_norm = torch.norm(feat1).item()
        feat2_norm = torch.norm(feat2).item()
        cosine_sim = torch.cosine_similarity(feat1, feat2, dim=1).item()

        print(f"  Feature 1 norm: {feat1_norm:.4f}")
        print(f"  Feature 2 norm: {feat2_norm:.4f}")
        print(f"  Cosine similarity: {cosine_sim:.4f}")

        # 预训练模型应该产生有意义的特征（非零且不完全相同）
        if feat1_norm > 0.1 and feat2_norm > 0.1:
            print("  ✅ Features have reasonable magnitudes")
        else:
            print("  ⚠️  Features seem too small (possible initialization issue)")

        # Note: High cosine similarity for random inputs is normal for well-trained models
        # as they tend to produce normalized, high-quality features
        if abs(cosine_sim) < 1.0:  # Just check they're not exactly identical
            print("  ✅ Features show some diversity (not exactly identical)")
        else:
            print("  ⚠️  Features are identical (possible issue)")

        # 测试特征的稳定性（同一输入应该产生相同输出）
        features1_repeat = model.forward_features(input1)
        if isinstance(features1_repeat, dict):
            feat1_repeat = features1_repeat['x_norm_clstoken']
        else:
            feat1_repeat = features1_repeat[:, 0]

        consistency = torch.allclose(feat1, feat1_repeat, atol=1e-6)
        print(f"  ✅ Model output is consistent: {consistency}")


def test_multiscale_features(backbone, decoder, model_name):
    """测试多尺度特征提取和融合"""
    print(f"\n🔬 Testing multi-scale feature extraction for {model_name}:")

    backbone.eval()
    decoder.eval()

    layer_indices = intermediate_layer_idx['vitl']  # [4, 11, 17, 23]

    with torch.no_grad():
        # Test different input sizes
        test_sizes = [224, 448, 512]

        for img_size in test_sizes:
            print(f"\n  📏 Testing input size: {img_size}x{img_size}")

            # Create test input
            test_input = torch.randn(1, 3, img_size, img_size)

            try:
                # Extract multi-scale features
                fused_features = extract_multiscale_features(
                    backbone, decoder, test_input, layer_indices
                )

                # Print feature map information
                for i, feat in enumerate(fused_features):
                    scale_name = f"Scale_{i+1}"
                    print(f"    {scale_name}: {feat.shape}")

                    # Check feature statistics
                    feat_mean = feat.mean().item()
                    feat_std = feat.std().item()
                    print(f"      Mean: {feat_mean:.6f}, Std: {feat_std:.6f}")

                # Test feature consistency
                fused_features_repeat = extract_multiscale_features(
                    backbone, decoder, test_input, layer_indices
                )

                all_consistent = all(
                    torch.allclose(f1, f2, atol=1e-6)
                    for f1, f2 in zip(fused_features, fused_features_repeat)
                )
                print(f"    ✅ Multi-scale features consistent: {all_consistent}")

            except Exception as e:
                print(f"    ❌ Failed for size {img_size}: {str(e)}")
                continue


def build_dpt_depth_model(backbone, depth_ckpt_path=None):
    """Build DPT depth model using the backbone."""
    # Use FOUR_EVEN_INTERVALS policy for ViT-L (will internally map to [4,11,17,23])
    backbone_out_layers = BackboneLayersSet.FOUR_EVEN_INTERVALS
    depther = build_depther(
        backbone=backbone,
        backbone_out_layers=backbone_out_layers,
        n_output_channels=256,  # bins for regression-by-classification
        use_backbone_norm=False,
        use_batchnorm=False,
        use_cls_token=False,
        head_type="dpt",
        min_depth=0.001,
        max_depth=10.0,
        bins_strategy="linear",
        norm_strategy="linear",
    )
    if depth_ckpt_path:
        load_partial_weights(depther, depth_ckpt_path, model_name="Depther")
    return depther, backbone_out_layers


def test_depth_model_dimensions():
    """Test the dimensions of the depth model."""
    print("=" * 60)
    print("DINOv3 + DPT Depth Model Dimension Testing")
    print("=" * 60)
    test_sizes = [224, 448, 512, 768, 896, 1024]
    backbone_ckpt = "checkpoint/dinov3_vitl16_pretrain_lvd1689m-8aa4cbdd.pth"
    depth_ckpt = "checkpoint/dpt_head.pth"  # optional
    print(f"\n1. Loading backbone from {backbone_ckpt} ...")
    if os.path.exists(backbone_ckpt):
        try:
            backbone = load_dinov3_vitl16_local(backbone_ckpt)
        except Exception as e:
            print(f"❌ Backbone load failed: {e}")
            backbone = dinov3_vitl16(pretrained=False)
    else:
        print("⚠️  Backbone checkpoint not found, using random init.")
        backbone = dinov3_vitl16(pretrained=False)
    print_model_info(backbone, "Backbone")
    verify_weights_loaded(backbone, "Backbone")
    test_inference_quality(backbone, "Backbone")
    print("\n2. Building DPT depth model ...")
    depther, used_layers = build_dpt_depth_model(backbone, depth_ckpt_path=depth_ckpt if os.path.exists(depth_ckpt) else None)
    depther.eval()
    print_model_info(depther, "Depth Model (Sequential)")
    print(f"Used backbone layer selection: {used_layers}")
    print("\n3. Testing different input sizes:")
    print("-" * 55)
    for img_size in test_sizes:
        print(f"\n>> Input size: {img_size}x{img_size}")
        dummy = torch.randn(1, 3, img_size, img_size)
        try:
            with torch.no_grad():
                # Inspect intermediate backbone features via encoder wrapper
                encoder = depther[0].encoder  # EncoderDecoder -> .encoder
                inter_feats = encoder(dummy)  # list of (feat_map, cls_token)
                for i, (feat, cls_tok) in enumerate(inter_feats):
                    print(f"  Layer {i} feature: {feat.shape}, cls: {cls_tok.shape}")
                depth_pred = depther(dummy)
                if isinstance(depth_pred, torch.Tensor):
                    print(f"  Depth output: {depth_pred.shape}")
                    h_out, w_out = depth_pred.shape[-2:]
                else:
                    # Sequential ends with tensor, but keep safe
                    first_tensor = None
                    if isinstance(depth_pred, (list, tuple)) and depth_pred and isinstance(depth_pred[0], torch.Tensor):
                        first_tensor = depth_pred[0]
                    elif isinstance(depth_pred, dict):
                        for v in depth_pred.values():
                            if isinstance(v, torch.Tensor):
                                first_tensor = v; break
                    if first_tensor is not None:
                        print(f"  Depth output(tensor): {first_tensor.shape}")
                        h_out, w_out = first_tensor.shape[-2:]
                    else:
                        print("  ❌ No tensor depth output found")
                        continue
                status = "OK" if (h_out == img_size and w_out == img_size) else "MISMATCH"
                print(f"  Spatial size check: {h_out}x{w_out} vs {img_size} -> {status}")
        except Exception as e:
            print(f"  ❌ Failed at size {img_size}: {e}")
            continue
    print("\n4. Complexity Approximation:")
    for img_size in [224, 512, 1024]:
        n_patches = (img_size // 16) ** 2
        approx_flops = n_patches ** 2 * backbone.embed_dim * len(backbone.blocks)
        mem_tokens_mb = n_patches * backbone.embed_dim * 4 / 1024**2
        print(f"  {img_size}x{img_size}: patches={n_patches}, ~FLOPs={approx_flops/1e9:.2f}G, token_mem≈{mem_tokens_mb:.2f}MB")


def create_dummy_image(size=224):
    """创建用于测试的虚拟图像"""
    # 创建彩色渐变图像
    image = np.zeros((size, size, 3), dtype=np.uint8)
    for i in range(size):
        for j in range(size):
            image[i, j] = [
                int(255 * i / size),  # Red gradient
                int(255 * j / size),  # Green gradient  
                int(255 * (i + j) / (2 * size))  # Blue gradient
            ]
    return Image.fromarray(image)


def test_data_pipeline():
    """测试数据预处理管道"""
    print("\nData Pipeline Test:")
    print("-" * 30)
    
    # 创建测试图像
    test_img = create_dummy_image(256)
    print(f"Original image size: {test_img.size}")
    
    # 定义变换
    def make_transform(resize_size: int = 768):
        return transforms.Compose([
            transforms.ToTensor(),
            transforms.Resize((resize_size, resize_size), antialias=True),
            transforms.Normalize(
                mean=(0.485, 0.456, 0.406),
                std=(0.229, 0.224, 0.225),
            )
        ])
    
    # 测试不同尺寸的变换
    for size in [224, 512, 768]:
        transform = make_transform(size)
        transformed = transform(test_img)
        print(f"Transformed to {size}x{size}: {transformed.shape}")
        print(f"  Value range: [{transformed.min():.3f}, {transformed.max():.3f}]")


def test_multiscale_dinov3():
    """Test multi-scale feature extraction with DINOv3 and DPTHead_decoder"""
    print("=" * 60)
    print("DINOv3 Multi-Scale Feature Extraction Testing")
    print("=" * 60)

    # Load backbone
    checkpoint_path = "checkpoint/dinov3_vitl16_pretrain_lvd1689m-8aa4cbdd.pth"
    print(f"\n1. Loading DINOv3-ViT-L/16 backbone from {checkpoint_path}...")

    if os.path.exists(checkpoint_path):
        try:
            backbone = load_dinov3_vitl16_local(checkpoint_path)
        except Exception as e:
            print(f"❌ Failed to load from local checkpoint: {str(e)}")
            print("Falling back to model without pretrained weights...")
            backbone = dinov3_vitl16(pretrained=False)
    else:
        print(f"❌ Checkpoint file not found: {checkpoint_path}")
        print("Loading model without pretrained weights...")
        backbone = dinov3_vitl16(pretrained=False)

    print_model_info(backbone, "DINOv3-ViT-L/16 Backbone")
    verify_weights_loaded(backbone, "DINOv3-ViT-L/16 Backbone")
    test_inference_quality(backbone, "DINOv3-ViT-L/16 Backbone")

    # Create multi-scale feature extractor
    print("\n2. Creating multi-scale feature extractor...")
    multiscale_decoder = create_multiscale_feature_extractor(
        backbone,
        layer_indices=intermediate_layer_idx['vitl'],
        features=256
    )
    print_model_info(multiscale_decoder, "Multi-scale DPT Decoder")

    # Test multi-scale feature extraction
    test_multiscale_features(backbone, multiscale_decoder, "DINOv3-ViT-L/16")

    # Test different input sizes with multi-scale extraction
    print("\n3. Testing multi-scale extraction on different input sizes:")
    print("-" * 55)

    test_sizes = [224, 448, 512, 768]
    layer_indices = intermediate_layer_idx['vitl']  # [4, 11, 17, 23]

    backbone.eval()
    multiscale_decoder.eval()

    for img_size in test_sizes:
        print(f"\n📏 Testing input size: {img_size}x{img_size}")

        # Create random input
        dummy_input = torch.randn(1, 3, img_size, img_size)

        try:
            with torch.no_grad():
                # Extract intermediate layer features using get_intermediate_layers
                print(f"  🔍 Extracting features from layers {layer_indices}")
                intermediate_features = backbone.get_intermediate_layers(
                    dummy_input,
                    n=layer_indices,
                    reshape=False,
                    return_class_token=True,
                    return_extra_tokens=False,
                    norm=True
                )

                # Print intermediate feature information
                for i, (patch_tokens, cls_token) in enumerate(intermediate_features):
                    layer_idx = layer_indices[i]
                    print(f"    Layer {layer_idx}: patch_tokens {patch_tokens.shape}, cls_token {cls_token.shape}")

                # Extract fused multi-scale features
                fused_features = extract_multiscale_features(
                    backbone, multiscale_decoder, dummy_input, layer_indices
                )

                print(f"  🔗 Fused multi-scale features:")
                for i, feat in enumerate(fused_features):
                    scale_name = f"Scale_{i+1}"
                    print(f"    {scale_name}: {feat.shape}")

                    # Calculate effective receptive field and stride
                    input_h, input_w = img_size, img_size
                    feat_h, feat_w = feat.shape[-2:]
                    effective_stride_h = input_h / feat_h
                    effective_stride_w = input_w / feat_w
                    print(f"      Effective stride: {effective_stride_h:.1f}x{effective_stride_w:.1f}")

        except Exception as e:
            print(f"  ❌ Failed for size {img_size}: {str(e)}")
            continue


if __name__ == "__main__":
    # 设置设备
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"Using device: {device}")

    # 运行测试
    test_multiscale_dinov3()  # New multi-scale test
    test_depth_model_dimensions()  # Existing depth test
    test_data_pipeline()

    print("\n" + "=" * 60)
    print("All testing completed.")
    print("=" * 60)