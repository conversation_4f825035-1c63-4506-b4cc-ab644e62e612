import sys
import os
# Add the DINOv3 repo path - adjust this to your actual path
REPO_DIR = "/Users/<USER>/Downloads/dinov3-main"
sys.path.append(REPO_DIR)

import torch
from torchvision import transforms
import numpy as np
from PIL import Image

from dinov3.models.vision_transformer import DinoVisionTransformer
from dinov3.eval.segmentation.models import build_segmentation_decoder
from dinov3.hub.backbones import dinov3_vit7b16


def print_model_info(model, model_name="Model"):
    """打印模型的参数信息"""
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"\n{model_name} Info:")
    print(f"  Total parameters: {total_params:,}")
    print(f"  Trainable parameters: {trainable_params:,}")
    print(f"  Model size: {total_params * 4 / 1024**2:.2f} MB (fp32)")


def test_model_dimensions():
    """测试模型的输入输出尺寸"""
    print("=" * 60)
    print("DINOv3 Model Dimension Testing (No Pretrained Weights)")
    print("=" * 60)
    
    # 测试不同的输入尺寸
    test_sizes = [224, 448, 512, 768, 896, 1024]
    
    # 创建不带预训练权重的backbone
    print("\n1. Creating DINOv3-ViT7B/16 backbone without pretrained weights...")
    backbone = dinov3_vit7b16(pretrained=False)
    print_model_info(backbone, "DINOv3-ViT7B/16 Backbone")
    
    # 创建分割模型(不带预训练权重)
    print("\n2. Creating segmentation model...")
    segmentor = build_segmentation_decoder(
        backbone_model=backbone,
        backbone_name="dinov3_vit7b16",
        decoder_type="m2f",
        hidden_dim=2048,
        num_classes=150,
        autocast_dtype=torch.bfloat16,
    )
    print_model_info(segmentor, "Full Segmentation Model")
    
    # 将模型设为评估模式
    backbone.eval()
    segmentor.eval()
    
    print(f"\n3. Model Architecture Details:")
    print(f"   Patch size: {backbone.patch_size}")
    print(f"   Embed dim: {backbone.embed_dim}")
    print(f"   Num heads: {backbone.num_heads}")
    print(f"   Num layers: {len(backbone.blocks)}")
    
    # 测试不同输入尺寸
    print("\n4. Testing different input sizes:")
    print("-" * 50)
    
    for img_size in test_sizes:
        print(f"\nTesting input size: {img_size}x{img_size}")
        
        # 创建随机输入
        dummy_input = torch.randn(1, 3, img_size, img_size)
        
        try:
            with torch.no_grad():
                # 测试backbone输出
                backbone_features = backbone.forward_features(dummy_input)
                
                # 计算特征图尺寸
                patch_size = backbone.patch_size
                num_patches_h = img_size // patch_size
                num_patches_w = img_size // patch_size
                expected_seq_len = num_patches_h * num_patches_w + 1  # +1 for cls token
                
                print(f"  Input shape: {dummy_input.shape}")
                print(f"  Backbone output shape: {backbone_features.shape}")
                print(f"  Expected sequence length: {expected_seq_len}")
                print(f"  Feature map size: {num_patches_h}x{num_patches_w} patches")
                
                # 测试完整分割模型
                seg_output = segmentor(dummy_input)
                print(f"  Segmentation output shape: {seg_output.shape}")
                
                # 计算感受野信息
                effective_stride = patch_size
                receptive_field = patch_size
                print(f"  Effective stride: {effective_stride}")
                print(f"  Receptive field: {receptive_field}px")
                
        except Exception as e:
            print(f"  ❌ Failed for size {img_size}: {str(e)}")
            continue
    
    print("\n5. Memory and Computational Analysis:")
    print("-" * 40)
    
    # 分析不同输入尺寸的计算复杂度
    for img_size in [224, 512, 1024]:
        num_patches = (img_size // 16) ** 2
        # ViT的计算复杂度大致为 O(n^2 * d) 其中n是patch数量，d是维度
        flops_approx = num_patches ** 2 * backbone.embed_dim * len(backbone.blocks)
        memory_approx = num_patches * backbone.embed_dim * 4 / 1024**2  # MB
        
        print(f"  Size {img_size}x{img_size}:")
        print(f"    Patches: {num_patches}")
        print(f"    Approx FLOPs: {flops_approx/1e9:.2f}G")
        print(f"    Approx Memory: {memory_approx:.2f}MB")


def create_dummy_image(size=224):
    """创建用于测试的虚拟图像"""
    # 创建彩色渐变图像
    image = np.zeros((size, size, 3), dtype=np.uint8)
    for i in range(size):
        for j in range(size):
            image[i, j] = [
                int(255 * i / size),  # Red gradient
                int(255 * j / size),  # Green gradient  
                int(255 * (i + j) / (2 * size))  # Blue gradient
            ]
    return Image.fromarray(image)


def test_data_pipeline():
    """测试数据预处理管道"""
    print("\n6. Testing Data Pipeline:")
    print("-" * 30)
    
    # 创建测试图像
    test_img = create_dummy_image(256)
    print(f"Original image size: {test_img.size}")
    
    # 定义变换
    def make_transform(resize_size: int = 768):
        return transforms.Compose([
            transforms.ToTensor(),
            transforms.Resize((resize_size, resize_size), antialias=True),
            transforms.Normalize(
                mean=(0.485, 0.456, 0.406),
                std=(0.229, 0.224, 0.225),
            )
        ])
    
    # 测试不同尺寸的变换
    for size in [224, 512, 768]:
        transform = make_transform(size)
        transformed = transform(test_img)
        print(f"Transformed to {size}x{size}: {transformed.shape}")
        print(f"  Value range: [{transformed.min():.3f}, {transformed.max():.3f}]")


if __name__ == "__main__":
    # 设置设备
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"Using device: {device}")
    
    # 运行测试
    test_model_dimensions()
    test_data_pipeline()
    
    print("\n" + "=" * 60)
    print("Testing completed! No pretrained weights were loaded.")
    print("=" * 60)